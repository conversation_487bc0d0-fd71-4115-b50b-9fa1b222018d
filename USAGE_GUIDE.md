# 视频角点过滤器使用指南

## 🎯 程序功能

这个程序专门用于解决视频处理中的卡顿问题，通过以下方式优化视频质量：

1. **智能角点检测**: 使用Harris角点检测算法分析每一帧
2. **自适应过滤**: 自动过滤角点数量超过10的帧（通常是马赛克或噪声帧）
3. **流畅输出**: 优化编码器设置，确保输出视频播放流畅
4. **多格式支持**: 支持常见的视频格式（MP4、AVI、MOV等）

## 🚀 快速开始

### 第一步：编译程序

#### Windows用户
```bash
# 双击运行或在命令行执行
build_video_filter.bat
```

#### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x build_video_filter.sh

# 运行编译脚本
./build_video_filter.sh
```

#### 手动编译（如果脚本失败）
```bash
# 确保已安装OpenCV
g++ -std=c++17 -O2 src/smooth_video_filter.cpp -o smooth_video_filter `pkg-config --cflags --libs opencv4`
```

### 第二步：使用程序

```bash
# 基本用法
./smooth_video_filter input_video.mp4 output_video.mp4

# 示例
./smooth_video_filter original.mp4 filtered.mp4
```

## 📊 程序输出示例

```
========================================
     平滑视频角点过滤器 v2.0
   专为解决视频卡顿问题而优化
========================================

=== 输入视频信息 ===
文件: input.mp4
分辨率: 1920x1080
帧率: 30 FPS
总帧数: 1800
=====================

尝试编码器: H.264...
成功使用编码器: H.264

开始处理视频...
角点阈值: 10 (超过此值的帧将被过滤)

进度: 16.7% (300/1800) 当前帧角点: 5
进度: 33.3% (600/1800) 当前帧角点: 15
进度: 50.0% (900/1800) 当前帧角点: 3
进度: 66.7% (1200/1800) 当前帧角点: 8
进度: 83.3% (1500/1800) 当前帧角点: 12
进度: 100.0% (1800/1800) 当前帧角点: 6

=== 处理完成 ===
处理时间: 45 秒
总帧数: 1800
保留帧数: 1620
过滤帧数: 180
过滤比例: 10.0%
输出文件: output.mp4
=================

✅ 视频处理成功完成!
💡 提示: 如果输出视频仍有问题，请尝试使用不同的输出格式 (.mp4, .avi)
```

## 🔧 解决卡顿问题的关键优化

### 1. 多编码器支持
程序会自动尝试以下编码器（按优先级排序）：
- **H.264**: 最佳质量和兼容性
- **XVID**: 良好的兼容性
- **MJPEG**: 最佳兼容性，文件较大
- **MPEG-4**: 备选方案

### 2. 帧率保持
- 自动检测输入视频的帧率
- 输出视频保持相同帧率
- 避免帧率不匹配导致的卡顿

### 3. 分辨率保持
- 保持原始分辨率
- 避免不必要的缩放操作
- 减少处理开销

### 4. 内存优化
- 逐帧处理，避免内存溢出
- 及时释放处理完的帧
- 优化的缓冲区管理

## 🎛️ 自定义设置

### 修改角点阈值
如果需要调整过滤的敏感度，可以修改源代码：

```cpp
// 在 processVideo() 函数中找到这一行
if (corner_count <= 10) {  // 修改这个数值
    writer.write(frame);
    kept_frames++;
}
```

- **增大数值** (如15、20): 过滤更少的帧，保留更多内容
- **减小数值** (如5、8): 过滤更多的帧，输出更干净

### 修改Harris角点检测参数
在 `iCheckMosaicImage` 函数中：

```cpp
cornerHarris(srcFrameGray, srcFrameGray, 2, 3, 0.04);  // 调整这些参数
threshold(srcFrameGray, srcFrameGray, 0.010, 255, cv::THRESH_BINARY);  // 调整阈值
```

## 🐛 故障排除

### 常见问题及解决方案

#### 1. 编译错误
```
错误: 未找到OpenCV库
```
**解决方案**:
- **Ubuntu/Debian**: `sudo apt-get install libopencv-dev`
- **CentOS/RHEL**: `sudo yum install opencv-devel`
- **macOS**: `brew install opencv`
- **Windows**: 下载并安装OpenCV，配置环境变量

#### 2. 无法打开输入文件
```
错误: 无法打开输入视频: input.mp4
```
**解决方案**:
- 检查文件路径是否正确
- 确认文件格式被OpenCV支持
- 检查文件是否损坏
- 尝试使用绝对路径

#### 3. 无法创建输出文件
```
错误: 无法使用任何编码器创建输出视频
```
**解决方案**:
- 检查输出目录是否存在且有写权限
- 确认磁盘空间充足
- 尝试不同的输出格式 (.mp4, .avi, .mov)
- 检查OpenCV是否支持视频编码

#### 4. 输出视频仍然卡顿
**可能原因和解决方案**:
- **编码器问题**: 尝试不同的输出格式
- **播放器问题**: 使用VLC等专业播放器测试
- **阈值设置**: 调整角点检测阈值
- **硬件性能**: 在性能更好的机器上处理

### 性能优化建议

#### 1. 处理大文件
- 确保有足够的磁盘空间（至少是输入文件的2倍）
- 关闭其他占用CPU的程序
- 考虑分段处理超大文件

#### 2. 提高处理速度
- 使用SSD存储
- 增加系统内存
- 使用多核CPU

#### 3. 质量vs速度平衡
- 对于快速预览：使用MJPEG编码器
- 对于最终输出：使用H.264编码器
- 考虑降低分辨率进行快速测试

## 📝 技术细节

### 角点检测算法
- **算法**: Harris角点检测
- **参数**: 窗口大小=2, Sobel核大小=3, Harris参数=0.04
- **阈值**: 0.010 (可调整)

### 支持的视频格式
- **输入**: MP4, AVI, MOV, MKV, WMV, FLV
- **输出**: MP4, AVI, MOV (取决于编码器支持)

### 系统要求
- **操作系统**: Windows 7+, Linux, macOS
- **内存**: 最少2GB，推荐4GB+
- **OpenCV**: 3.2或更高版本
- **编译器**: GCC 7+, Clang 5+, MSVC 2017+

## 📞 获取帮助

如果遇到问题：
1. 查看本文档的故障排除部分
2. 检查 `VIDEO_FILTER_README.md` 获取更多技术细节
3. 确保系统满足所有依赖要求
4. 尝试使用不同的输入/输出格式

## 🔄 版本历史

- **v2.0**: 优化版本，专门解决卡顿问题
  - 多编码器支持
  - 改进的帧率处理
  - 更好的错误处理
  - 详细的进度显示

- **v1.0**: 基础版本
  - 基本的角点检测和过滤功能
