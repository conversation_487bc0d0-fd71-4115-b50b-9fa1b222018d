#include <iostream>
#include <string>
#include <opencv2/opencv.hpp>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libswscale/swscale.h>
#include <libavutil/imgutils.h>
#include <libavutil/opt.h>
}

using namespace cv;
using namespace std;

// 从CornerTest.cpp复制的角点检测函数
int iCheckMosaicImage(const Mat& srcFrame)
{
    Mat srcFrameGray;
    int l_iTotalCorner = 0;

    cvtColor(srcFrame, srcFrameGray, COLOR_BGR2GRAY);
    cornerHarris(srcFrameGray, srcFrameGray, 2, 3, 0.04);
    threshold(srcFrameGray, srcFrameGray, 0.010, 255, cv::THRESH_BINARY);

    int l_iRow = srcFrameGray.rows;
    int l_iCol = srcFrameGray.cols;

    for (int i = 0; i < l_iRow; i++)
    {
        for (int j = 0; j < l_iCol; j++)
        {
            if (srcFrameGray.at<float>(i, j) == 255) // 值255为角点
            {
                l_iTotalCorner++;
            }
        }
    }

    return l_iTotalCorner;
}

// 从CornerTest.cpp复制的AVFrame转Mat函数
cv::Mat avFrameToCvMat(AVFrame* frame, SwsContext* sws_ctx) 
{
    cv::Mat bgr(frame->height, frame->width, CV_8UC3);
    int stride = 3 * frame->width;
    sws_scale(sws_ctx, (const uint8_t* const*)frame->data, frame->linesize, 0, frame->height, &bgr.data, &stride);
    return bgr;
}

// Mat转AVFrame函数
AVFrame* cvMatToAVFrame(const cv::Mat& mat, SwsContext* sws_ctx, AVFrame* frame)
{
    if (!frame) {
        frame = av_frame_alloc();
        frame->format = AV_PIX_FMT_YUV420P;
        frame->width = mat.cols;
        frame->height = mat.rows;
        av_frame_get_buffer(frame, 32);
    }

    // 创建临时BGR24格式的AVFrame
    AVFrame* bgr_frame = av_frame_alloc();
    bgr_frame->format = AV_PIX_FMT_BGR24;
    bgr_frame->width = mat.cols;
    bgr_frame->height = mat.rows;
    av_frame_get_buffer(bgr_frame, 32);

    // 复制Mat数据到BGR24 AVFrame
    int stride = 3 * mat.cols;
    memcpy(bgr_frame->data[0], mat.data, mat.rows * stride);
    bgr_frame->linesize[0] = stride;

    // 转换BGR24到YUV420P
    sws_scale(sws_ctx, (const uint8_t* const*)bgr_frame->data, bgr_frame->linesize, 
              0, mat.rows, frame->data, frame->linesize);

    av_frame_free(&bgr_frame);
    return frame;
}

class VideoProcessor 
{
private:
    AVFormatContext* input_format_ctx;
    AVFormatContext* output_format_ctx;
    AVCodecContext* decoder_ctx;
    AVCodecContext* encoder_ctx;
    SwsContext* sws_ctx_decode;
    SwsContext* sws_ctx_encode;
    int video_stream_index;
    AVStream* input_stream;
    AVStream* output_stream;
    
public:
    VideoProcessor() : input_format_ctx(nullptr), output_format_ctx(nullptr),
                      decoder_ctx(nullptr), encoder_ctx(nullptr),
                      sws_ctx_decode(nullptr), sws_ctx_encode(nullptr),
                      video_stream_index(-1), input_stream(nullptr), output_stream(nullptr) {}
    
    ~VideoProcessor() {
        cleanup();
    }
    
    bool initInput(const string& input_file) {
        // 打开输入文件
        if (avformat_open_input(&input_format_ctx, input_file.c_str(), nullptr, nullptr) < 0) {
            cerr << "无法打开输入文件: " << input_file << endl;
            return false;
        }
        
        // 获取流信息
        if (avformat_find_stream_info(input_format_ctx, nullptr) < 0) {
            cerr << "无法获取流信息" << endl;
            return false;
        }
        
        // 查找视频流
        video_stream_index = av_find_best_stream(input_format_ctx, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
        if (video_stream_index < 0) {
            cerr << "无法找到视频流" << endl;
            return false;
        }
        
        input_stream = input_format_ctx->streams[video_stream_index];
        
        // 查找解码器
        const AVCodec* decoder = avcodec_find_decoder(input_stream->codecpar->codec_id);
        if (!decoder) {
            cerr << "无法找到解码器" << endl;
            return false;
        }
        
        // 创建解码器上下文
        decoder_ctx = avcodec_alloc_context3(decoder);
        if (!decoder_ctx) {
            cerr << "无法分配解码器上下文" << endl;
            return false;
        }
        
        // 复制参数
        if (avcodec_parameters_to_context(decoder_ctx, input_stream->codecpar) < 0) {
            cerr << "无法复制解码器参数" << endl;
            return false;
        }
        
        // 打开解码器
        if (avcodec_open2(decoder_ctx, decoder, nullptr) < 0) {
            cerr << "无法打开解码器" << endl;
            return false;
        }
        
        // 初始化解码用的SwsContext
        sws_ctx_decode = sws_getContext(decoder_ctx->width, decoder_ctx->height, decoder_ctx->pix_fmt,
                                       decoder_ctx->width, decoder_ctx->height, AV_PIX_FMT_BGR24,
                                       SWS_BICUBIC, nullptr, nullptr, nullptr);
        
        return true;
    }
    
    bool initOutput(const string& output_file) {
        // 分配输出格式上下文
        avformat_alloc_output_context2(&output_format_ctx, nullptr, nullptr, output_file.c_str());
        if (!output_format_ctx) {
            cerr << "无法创建输出格式上下文" << endl;
            return false;
        }
        
        // 查找编码器
        const AVCodec* encoder = avcodec_find_encoder(AV_CODEC_ID_H264);
        if (!encoder) {
            cerr << "无法找到H264编码器" << endl;
            return false;
        }
        
        // 创建输出流
        output_stream = avformat_new_stream(output_format_ctx, nullptr);
        if (!output_stream) {
            cerr << "无法创建输出流" << endl;
            return false;
        }
        
        // 创建编码器上下文
        encoder_ctx = avcodec_alloc_context3(encoder);
        if (!encoder_ctx) {
            cerr << "无法分配编码器上下文" << endl;
            return false;
        }
        
        // 设置编码器参数
        encoder_ctx->codec_id = AV_CODEC_ID_H264;
        encoder_ctx->bit_rate = decoder_ctx->bit_rate > 0 ? decoder_ctx->bit_rate : 400000;
        encoder_ctx->width = decoder_ctx->width;
        encoder_ctx->height = decoder_ctx->height;
        encoder_ctx->time_base = input_stream->time_base;
        encoder_ctx->framerate = input_stream->avg_frame_rate;
        encoder_ctx->pix_fmt = AV_PIX_FMT_YUV420P;
        encoder_ctx->gop_size = 12;
        encoder_ctx->max_b_frames = 1;
        
        // 设置编码器选项以减少延迟和提高质量
        av_opt_set(encoder_ctx->priv_data, "preset", "medium", 0);
        av_opt_set(encoder_ctx->priv_data, "crf", "23", 0);
        
        if (output_format_ctx->oformat->flags & AVFMT_GLOBALHEADER) {
            encoder_ctx->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;
        }
        
        // 打开编码器
        if (avcodec_open2(encoder_ctx, encoder, nullptr) < 0) {
            cerr << "无法打开编码器" << endl;
            return false;
        }
        
        // 复制编码器参数到流
        if (avcodec_parameters_from_context(output_stream->codecpar, encoder_ctx) < 0) {
            cerr << "无法复制编码器参数" << endl;
            return false;
        }
        
        output_stream->time_base = encoder_ctx->time_base;
        
        // 初始化编码用的SwsContext
        sws_ctx_encode = sws_getContext(decoder_ctx->width, decoder_ctx->height, AV_PIX_FMT_BGR24,
                                       encoder_ctx->width, encoder_ctx->height, encoder_ctx->pix_fmt,
                                       SWS_BICUBIC, nullptr, nullptr, nullptr);
        
        // 打开输出文件
        if (!(output_format_ctx->oformat->flags & AVFMT_NOFILE)) {
            if (avio_open(&output_format_ctx->pb, output_file.c_str(), AVIO_FLAG_WRITE) < 0) {
                cerr << "无法打开输出文件: " << output_file << endl;
                return false;
            }
        }
        
        // 写入文件头
        if (avformat_write_header(output_format_ctx, nullptr) < 0) {
            cerr << "无法写入文件头" << endl;
            return false;
        }
        
        return true;
    }
    
    bool processVideo() {
        AVPacket* packet = av_packet_alloc();
        AVFrame* frame = av_frame_alloc();
        AVFrame* output_frame = av_frame_alloc();
        
        int64_t pts = 0;
        int frame_count = 0;
        int filtered_count = 0;
        
        cout << "开始处理视频..." << endl;
        
        while (av_read_frame(input_format_ctx, packet) >= 0) {
            if (packet->stream_index == video_stream_index) {
                // 发送包到解码器
                if (avcodec_send_packet(decoder_ctx, packet) >= 0) {
                    // 接收解码后的帧
                    while (avcodec_receive_frame(decoder_ctx, frame) >= 0) {
                        frame_count++;
                        
                        // 转换为OpenCV Mat
                        cv::Mat mat = avFrameToCvMat(frame, sws_ctx_decode);
                        
                        // 检测角点数量
                        int corner_count = iCheckMosaicImage(mat);
                        
                        cout << "帧 " << frame_count << ": 角点数量 = " << corner_count;
                        
                        // 如果角点数量小于等于10，则保留该帧
                        if (corner_count <= 10) {
                            cout << " (保留)" << endl;
                            
                            // 转换Mat回AVFrame并编码
                            output_frame = cvMatToAVFrame(mat, sws_ctx_encode, output_frame);
                            output_frame->pts = pts++;
                            
                            // 编码帧
                            if (avcodec_send_frame(encoder_ctx, output_frame) >= 0) {
                                AVPacket* out_packet = av_packet_alloc();
                                while (avcodec_receive_packet(encoder_ctx, out_packet) >= 0) {
                                    av_packet_rescale_ts(out_packet, encoder_ctx->time_base, output_stream->time_base);
                                    out_packet->stream_index = output_stream->index;
                                    av_interleaved_write_frame(output_format_ctx, out_packet);
                                }
                                av_packet_free(&out_packet);
                            }
                        } else {
                            cout << " (过滤)" << endl;
                            filtered_count++;
                        }
                    }
                }
            }
            av_packet_unref(packet);
        }
        
        // 刷新编码器
        avcodec_send_frame(encoder_ctx, nullptr);
        AVPacket* out_packet = av_packet_alloc();
        while (avcodec_receive_packet(encoder_ctx, out_packet) >= 0) {
            av_packet_rescale_ts(out_packet, encoder_ctx->time_base, output_stream->time_base);
            out_packet->stream_index = output_stream->index;
            av_interleaved_write_frame(output_format_ctx, out_packet);
        }
        av_packet_free(&out_packet);
        
        // 写入文件尾
        av_write_trailer(output_format_ctx);
        
        cout << "处理完成!" << endl;
        cout << "总帧数: " << frame_count << endl;
        cout << "过滤帧数: " << filtered_count << endl;
        cout << "保留帧数: " << (frame_count - filtered_count) << endl;
        
        av_packet_free(&packet);
        av_frame_free(&frame);
        av_frame_free(&output_frame);
        
        return true;
    }
    
    void cleanup() {
        if (sws_ctx_decode) {
            sws_freeContext(sws_ctx_decode);
            sws_ctx_decode = nullptr;
        }
        if (sws_ctx_encode) {
            sws_freeContext(sws_ctx_encode);
            sws_ctx_encode = nullptr;
        }
        if (decoder_ctx) {
            avcodec_free_context(&decoder_ctx);
        }
        if (encoder_ctx) {
            avcodec_free_context(&encoder_ctx);
        }
        if (input_format_ctx) {
            avformat_close_input(&input_format_ctx);
        }
        if (output_format_ctx) {
            if (!(output_format_ctx->oformat->flags & AVFMT_NOFILE)) {
                avio_closep(&output_format_ctx->pb);
            }
            avformat_free_context(output_format_ctx);
        }
    }
};

int main(int argc, char* argv[]) {
    if (argc != 3) {
        cout << "用法: " << argv[0] << " <输入视频文件> <输出视频文件>" << endl;
        return -1;
    }
    
    string input_file = argv[1];
    string output_file = argv[2];
    
    // 初始化FFmpeg
    av_register_all();
    avcodec_register_all();
    
    VideoProcessor processor;
    
    if (!processor.initInput(input_file)) {
        cerr << "初始化输入失败" << endl;
        return -1;
    }
    
    if (!processor.initOutput(output_file)) {
        cerr << "初始化输出失败" << endl;
        return -1;
    }
    
    if (!processor.processVideo()) {
        cerr << "处理视频失败" << endl;
        return -1;
    }
    
    return 0;
}
