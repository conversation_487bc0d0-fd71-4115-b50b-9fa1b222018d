# 视频时间戳处理技术指南

## 🎯 为什么时间戳处理很重要？

当你跳过视频帧时，如果不正确处理PTS（Presentation Time Stamp）和DTS（Decode Time Stamp），会导致：

1. **播放卡顿** - 播放器检测到时间戳跳跃
2. **音视频不同步** - 时间轴混乱
3. **播放速度异常** - 快进或慢放效果
4. **播放器错误** - 某些播放器可能拒绝播放

## 📊 时间戳类型说明

### PTS (Presentation Time Stamp)
- **作用**: 告诉播放器何时显示这一帧
- **特点**: 必须单调递增
- **单位**: 基于时间基准（time_base）

### DTS (Decode Time Stamp)  
- **作用**: 告诉解码器何时解码这一帧
- **特点**: 必须单调递增，且 DTS ≤ PTS
- **重要性**: 对于B帧尤其重要

### Time Base
- **定义**: 时间单位，如 1/90000 秒
- **作用**: PTS和DTS的计算基准
- **常用值**: 90000Hz（视频）、48000Hz（音频）

## 🔧 三种处理方案对比

### 方案1: OpenCV简单版本（有问题）

<augment_code_snippet path="src/smooth_video_filter.cpp" mode="EXCERPT">
````cpp
// 问题：直接跳过帧，不处理时间戳
if (corner_count <= 10) {
    writer.write(frame);  // OpenCV自动处理，但可能不正确
    kept_frames++;
} else {
    filtered_frames++;   // 直接跳过，造成时间戳跳跃
}
````
</augment_code_snippet>

**问题**:
- OpenCV的VideoWriter不能精确控制时间戳
- 跳过帧会导致播放时间轴不连续
- 可能产生卡顿或播放异常

### 方案2: FFmpeg基础版本（部分正确）

<augment_code_snippet path="src/video_filter.cpp" mode="EXCERPT">
````cpp
// 问题：PTS递增但没有考虑跳帧的影响
output_frame->pts = pts++;
avcodec_send_frame(encoder_ctx, output_frame);
````
</augment_code_snippet>

**问题**:
- PTS递增步长固定，不考虑跳过的帧
- 输出视频的播放速度会变快
- 时间轴压缩，与原视频不匹配

### 方案3: 时间戳感知版本（正确）✅

<augment_code_snippet path="src/video_filter_with_timestamps.cpp" mode="EXCERPT">
````cpp
// 正确：基于帧率计算恒定的时间间隔
int64_t frame_duration = av_rescale_q(1, av_inv_q(encoder_ctx->framerate), output_time_base);
output_frame->pts = output_pts;
output_frame->pkt_dts = output_pts;

// 编码后递增PTS
output_pts += frame_duration;
````
</augment_code_snippet>

**优点**:
- 保持恒定的帧间隔
- 输出视频播放流畅
- 时间轴正确

## 🎬 实际效果对比

### 原始视频
```
帧序号: 1    2    3    4    5    6    7    8    9    10
PTS:    0   1000 2000 3000 4000 5000 6000 7000 8000 9000
角点:   5   15   8    12   6    20   7    18   9    11
```

### 方案1结果（OpenCV）
```
保留帧: 1    3    5    7    9
PTS:    0   1000 2000 3000 4000  ← 时间戳跳跃！
实际播放时间: 4秒，但内容是10秒的 → 播放过快
```

### 方案2结果（FFmpeg基础）
```
保留帧: 1    3    5    7    9
PTS:    0   1000 2000 3000 4000  ← 连续但压缩了
实际播放时间: 4秒，但内容是10秒的 → 播放过快
```

### 方案3结果（时间戳感知）✅
```
保留帧: 1    3    5    7    9
PTS:    0   2000 4000 6000 8000  ← 保持原始间隔！
实际播放时间: 8秒，内容也是8秒的 → 播放正常
```

## 🔍 关键代码解析

### 1. 时间基准设置
```cpp
// 使用高精度时间基准
encoder_ctx->time_base = av_make_q(1, 90000); // 90kHz
```

### 2. 帧间隔计算
```cpp
// 基于帧率计算每帧的时间间隔
int64_t frame_duration = av_rescale_q(1, av_inv_q(encoder_ctx->framerate), output_time_base);
```

### 3. PTS递增策略
```cpp
if (corner_count <= 10) {
    // 保留帧：设置PTS并递增
    output_frame->pts = output_pts;
    output_pts += frame_duration;
} else {
    // 跳过帧：不递增PTS，保持时间轴连续
}
```

### 4. 包时间戳重新计算
```cpp
// 重新计算包的时间戳
av_packet_rescale_ts(out_packet, encoder_ctx->time_base, output_stream->time_base);

// 确保DTS <= PTS
if (out_packet->dts > out_packet->pts) {
    out_packet->dts = out_packet->pts;
}
```

## 🚀 性能优化技巧

### 1. 时间基准选择
```cpp
// 推荐：90kHz（MPEG标准）
encoder_ctx->time_base = av_make_q(1, 90000);

// 避免：过低的时间基准
// encoder_ctx->time_base = av_make_q(1, 25); // 精度不够
```

### 2. 编码器优化
```cpp
// 减少延迟的设置
av_opt_set(encoder_ctx->priv_data, "preset", "medium", 0);
av_opt_set(encoder_ctx->priv_data, "tune", "film", 0);
av_opt_set(encoder_ctx->priv_data, "crf", "23", 0);
```

### 3. GOP设置
```cpp
// 合理的GOP大小
encoder_ctx->gop_size = 12;  // 0.5秒一个关键帧（30fps）
encoder_ctx->max_b_frames = 1; // 限制B帧数量
```

## 🔧 调试技巧

### 1. 时间戳日志
```cpp
cout << "帧 " << processed_frames 
     << ": 角点=" << corner_count 
     << ", 输入PTS=" << frame->pts 
     << ", 输出PTS=" << output_pts << endl;
```

### 2. 时间基准转换检查
```cpp
double time_base_ratio = av_q2d(input_time_base) / av_q2d(output_time_base);
cout << "时间基准转换比例: " << time_base_ratio << endl;
```

### 3. 播放时长验证
```bash
# 检查输出视频时长
ffprobe -v quiet -show_entries format=duration -of csv="p=0" output.mp4

# 检查帧率
ffprobe -v quiet -show_entries stream=r_frame_rate -of csv="p=0" output.mp4
```

## 📋 最佳实践总结

1. **始终使用FFmpeg处理时间戳** - OpenCV无法精确控制
2. **保持恒定的帧间隔** - 基于目标帧率计算
3. **正确设置时间基准** - 推荐90kHz
4. **验证DTS ≤ PTS** - 避免播放器错误
5. **测试多种播放器** - 确保兼容性

## 🎯 推荐使用

对于需要跳帧的视频处理，强烈推荐使用：
- **`video_filter_with_timestamps.cpp`** - 完整的时间戳处理
- 避免使用简单的OpenCV版本进行跳帧操作
- 在生产环境中务必验证输出视频的播放质量

## 🔗 相关资源

- [FFmpeg时间戳文档](https://ffmpeg.org/doxygen/trunk/group__lavc__core.html)
- [MPEG时间戳标准](https://en.wikipedia.org/wiki/Presentation_timestamp)
- [视频编码最佳实践](https://trac.ffmpeg.org/wiki/Encode/H.264)
