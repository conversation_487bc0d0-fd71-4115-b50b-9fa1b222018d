#!/bin/bash

echo "========================================"
echo "     编译视频角点过滤器"
echo "========================================"
echo

# 检查是否存在OpenCV
echo "检查OpenCV安装..."
if pkg-config --exists opencv4; then
    OPENCV_PKG="opencv4"
    echo "找到OpenCV4"
elif pkg-config --exists opencv; then
    OPENCV_PKG="opencv"
    echo "找到OpenCV"
else
    echo "错误: 未找到OpenCV库"
    echo "请确保已安装OpenCV并配置了pkg-config"
    echo
    echo "Ubuntu/Debian安装命令:"
    echo "  sudo apt-get install libopencv-dev"
    echo
    echo "CentOS/RHEL安装命令:"
    echo "  sudo yum install opencv-devel"
    echo
    echo "macOS安装命令:"
    echo "  brew install opencv"
    exit 1
fi

echo "使用OpenCV包: $OPENCV_PKG"
echo

# 创建build目录
mkdir -p build
cd build

echo "开始编译..."
echo

# 方法1: 使用CMake (推荐)
echo "=== 方法1: 使用CMake ==="
if cmake .. -DCMAKE_BUILD_TYPE=Release; then
    echo "CMake配置成功，开始构建..."
    if make smooth_video_filter -j$(nproc 2>/dev/null || echo 4); then
        echo
        echo "✅ 编译成功！"
        echo "可执行文件位置: build/src/smooth_video_filter"
        
        # 复制到根目录方便使用
        cp src/smooth_video_filter ../
        echo "已复制到根目录: smooth_video_filter"
        
        # 设置执行权限
        chmod +x ../smooth_video_filter
        
        echo
        echo "========================================"
        echo "           编译完成！"
        echo "========================================"
        echo
        echo "使用方法:"
        echo "  ./smooth_video_filter input.mp4 output.mp4"
        echo
        echo "示例:"
        echo "  ./smooth_video_filter test_video.mp4 filtered_video.mp4"
        echo
        exit 0
    else
        echo "CMake构建失败，尝试方法2..."
    fi
else
    echo "CMake配置失败，尝试方法2..."
fi

echo
echo "=== 方法2: 直接编译 ==="
cd ..

# 获取OpenCV编译标志
OPENCV_CFLAGS=$(pkg-config --cflags $OPENCV_PKG)
OPENCV_LIBS=$(pkg-config --libs $OPENCV_PKG)

echo "编译标志: $OPENCV_CFLAGS"
echo "链接库: $OPENCV_LIBS"
echo

# 编译程序
echo "编译 smooth_video_filter..."
if g++ -std=c++17 -O2 src/smooth_video_filter.cpp -o smooth_video_filter $OPENCV_CFLAGS $OPENCV_LIBS; then
    echo
    echo "✅ 编译成功！"
    echo "可执行文件: smooth_video_filter"
    
    # 设置执行权限
    chmod +x smooth_video_filter
    
    echo
    echo "========================================"
    echo "           编译完成！"
    echo "========================================"
    echo
    echo "使用方法:"
    echo "  ./smooth_video_filter input.mp4 output.mp4"
    echo
    echo "示例:"
    echo "  ./smooth_video_filter test_video.mp4 filtered_video.mp4"
    echo
else
    echo
    echo "❌ 编译失败"
    echo
    echo "可能的解决方案:"
    echo "1. 确保已安装OpenCV开发库"
    echo "   Ubuntu/Debian: sudo apt-get install libopencv-dev"
    echo "   CentOS/RHEL: sudo yum install opencv-devel"
    echo "   macOS: brew install opencv"
    echo
    echo "2. 确保已安装g++编译器"
    echo "   Ubuntu/Debian: sudo apt-get install build-essential"
    echo "   CentOS/RHEL: sudo yum groupinstall 'Development Tools'"
    echo "   macOS: xcode-select --install"
    echo
    echo "3. 确保pkg-config正确配置"
    echo "   检查命令: pkg-config --modversion $OPENCV_PKG"
    echo
    echo "如需帮助，请查看 VIDEO_FILTER_README.md"
    echo
    exit 1
fi
