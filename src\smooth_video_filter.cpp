#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <iomanip>
#include <opencv2/opencv.hpp>

using namespace cv;
using namespace std;

// 角点检测函数 - 从CornerTest.cpp复制并优化
int iCheckMosaicImage(const Mat& srcFrame)
{
    Mat srcFrameGray;
    int l_iTotalCorner = 0;

    // 转换为灰度图
    cvtColor(srcFrame, srcFrameGray, COLOR_BGR2GRAY);
    
    // Harris角点检测
    cornerHarris(srcFrameGray, srcFrameGray, 2, 3, 0.04);
    threshold(srcFrameGray, srcFrameGray, 0.010, 255, cv::THRESH_BINARY);

    int l_iRow = srcFrameGray.rows;
    int l_iCol = srcFrameGray.cols;

    // 计算角点数量
    for (int i = 0; i < l_iRow; i++)
    {
        for (int j = 0; j < l_iCol; j++)
        {
            if (srcFrameGray.at<float>(i, j) == 255) // 值255为角点
            {
                l_iTotalCorner++;
            }
        }
    }

    return l_iTotalCorner;
}

class SmoothVideoFilter 
{
private:
    VideoCapture cap;
    VideoWriter writer;
    string input_path;
    string output_path;
    
    // 视频属性
    int frame_width;
    int frame_height;
    double fps;
    int total_frames;
    
    // 统计信息
    int processed_frames;
    int filtered_frames;
    int kept_frames;
    
public:
    SmoothVideoFilter(const string& input, const string& output) 
        : input_path(input), output_path(output), 
          processed_frames(0), filtered_frames(0), kept_frames(0) {}
    
    bool initialize() {
        // 打开输入视频
        cap.open(input_path);
        if (!cap.isOpened()) {
            cerr << "错误: 无法打开输入视频: " << input_path << endl;
            return false;
        }
        
        // 获取视频属性
        frame_width = static_cast<int>(cap.get(CAP_PROP_FRAME_WIDTH));
        frame_height = static_cast<int>(cap.get(CAP_PROP_FRAME_HEIGHT));
        fps = cap.get(CAP_PROP_FPS);
        total_frames = static_cast<int>(cap.get(CAP_PROP_FRAME_COUNT));
        
        // 确保帧率有效
        if (fps <= 0 || fps > 120) {
            fps = 30.0; // 默认帧率
            cout << "警告: 检测到无效帧率，使用默认值30 FPS" << endl;
        }
        
        cout << "=== 输入视频信息 ===" << endl;
        cout << "文件: " << input_path << endl;
        cout << "分辨率: " << frame_width << "x" << frame_height << endl;
        cout << "帧率: " << fps << " FPS" << endl;
        cout << "总帧数: " << total_frames << endl;
        cout << "=====================" << endl << endl;
        
        return initializeWriter();
    }
    
private:
    bool initializeWriter() {
        // 尝试多种编码器以确保兼容性和流畅性
        vector<int> codecs = {
            VideoWriter::fourcc('H', '2', '6', '4'),  // H.264 - 最佳质量
            VideoWriter::fourcc('X', 'V', 'I', 'D'),  // XVID - 良好兼容性
            VideoWriter::fourcc('M', 'J', 'P', 'G'),  // MJPEG - 最佳兼容性
            VideoWriter::fourcc('M', 'P', '4', 'V'),  // MPEG-4
        };
        
        vector<string> codec_names = {"H.264", "XVID", "MJPEG", "MPEG-4"};
        
        for (size_t i = 0; i < codecs.size(); i++) {
            cout << "尝试编码器: " << codec_names[i] << "..." << endl;
            
            writer.open(output_path, codecs[i], fps, Size(frame_width, frame_height), true);
            
            if (writer.isOpened()) {
                cout << "成功使用编码器: " << codec_names[i] << endl << endl;
                return true;
            } else {
                cout << "编码器 " << codec_names[i] << " 不可用" << endl;
                writer.release(); // 确保释放
            }
        }
        
        cerr << "错误: 无法使用任何编码器创建输出视频" << endl;
        return false;
    }
    
public:
    bool processVideo() {
        Mat frame;
        vector<Mat> frame_buffer; // 帧缓冲区，用于平滑处理
        const int buffer_size = 5; // 缓冲区大小
        
        cout << "开始处理视频..." << endl;
        cout << "角点阈值: 10 (超过此值的帧将被过滤)" << endl << endl;
        
        auto start_time = chrono::high_resolution_clock::now();
        
        while (true) {
            bool ret = cap.read(frame);
            if (!ret || frame.empty()) {
                break; // 视频结束
            }
            
            processed_frames++;
            
            // 检测角点
            int corner_count = iCheckMosaicImage(frame);
            
            // 显示进度 (每100帧或每秒显示一次)
            if (processed_frames % max(1, static_cast<int>(fps)) == 0) {
                double progress = (double)processed_frames / total_frames * 100.0;
                cout << "进度: " << fixed << setprecision(1) << progress << "% "
                     << "(" << processed_frames << "/" << total_frames << ") "
                     << "当前帧角点: " << corner_count << endl;
            }
            
            // 决定是否保留帧
            if (corner_count <= 10) {
                // 保留帧
                writer.write(frame);
                kept_frames++;
            } else {
                // 过滤帧
                filtered_frames++;
                
                // 可选：为了保持视频流畅，可以重复上一帧
                // 这里我们选择直接跳过，让视频更紧凑
            }
        }
        
        auto end_time = chrono::high_resolution_clock::now();
        auto duration = chrono::duration_cast<chrono::seconds>(end_time - start_time);
        
        cout << "\n=== 处理完成 ===" << endl;
        cout << "处理时间: " << duration.count() << " 秒" << endl;
        cout << "总帧数: " << processed_frames << endl;
        cout << "保留帧数: " << kept_frames << endl;
        cout << "过滤帧数: " << filtered_frames << endl;
        cout << "过滤比例: " << fixed << setprecision(1) 
             << (double)filtered_frames / processed_frames * 100.0 << "%" << endl;
        cout << "输出文件: " << output_path << endl;
        cout << "=================" << endl;
        
        return true;
    }
    
    void cleanup() {
        if (cap.isOpened()) {
            cap.release();
        }
        if (writer.isOpened()) {
            writer.release();
        }
    }
    
    ~SmoothVideoFilter() {
        cleanup();
    }
};

// 检查文件是否存在
bool fileExists(const string& filename) {
    VideoCapture test_cap(filename);
    bool exists = test_cap.isOpened();
    test_cap.release();
    return exists;
}

// 获取文件扩展名
string getFileExtension(const string& filename) {
    size_t dot_pos = filename.find_last_of(".");
    if (dot_pos != string::npos) {
        return filename.substr(dot_pos);
    }
    return "";
}

int main(int argc, char* argv[]) {
    cout << "========================================" << endl;
    cout << "     平滑视频角点过滤器 v2.0" << endl;
    cout << "   专为解决视频卡顿问题而优化" << endl;
    cout << "========================================" << endl << endl;
    
    if (argc != 3) {
        cout << "用法: " << argv[0] << " <输入视频> <输出视频>" << endl;
        cout << "示例: " << argv[0] << " input.mp4 output.mp4" << endl;
        cout << "\n支持的格式: .mp4, .avi, .mov, .mkv, .wmv" << endl;
        return -1;
    }
    
    string input_file = argv[1];
    string output_file = argv[2];
    
    // 验证输入文件
    if (!fileExists(input_file)) {
        cerr << "错误: 输入文件不存在或无法读取: " << input_file << endl;
        return -1;
    }
    
    // 检查输出文件扩展名
    string output_ext = getFileExtension(output_file);
    if (output_ext.empty()) {
        cout << "警告: 输出文件没有扩展名，建议使用 .mp4" << endl;
    }
    
    // 创建处理器并运行
    SmoothVideoFilter processor(input_file, output_file);
    
    if (!processor.initialize()) {
        cerr << "初始化失败" << endl;
        return -1;
    }
    
    if (!processor.processVideo()) {
        cerr << "处理视频失败" << endl;
        return -1;
    }
    
    cout << "\n✅ 视频处理成功完成!" << endl;
    cout << "💡 提示: 如果输出视频仍有问题，请尝试使用不同的输出格式 (.mp4, .avi)" << endl;
    
    return 0;
}
