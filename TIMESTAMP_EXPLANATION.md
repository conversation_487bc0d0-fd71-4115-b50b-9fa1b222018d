# PTS vs DTS vs PKT_DTS 详细解释

## 🎯 三种时间戳的区别

### 1. PTS (Presentation Time Stamp)
- **位置**: `AVFrame.pts` 和 `AVPacket.pts`
- **含义**: 何时**显示**这一帧
- **特点**: 显示顺序的时间戳

### 2. DTS (Decode Time Stamp)  
- **位置**: `AVPacket.dts`
- **含义**: 何时**解码**这个数据包
- **特点**: 解码顺序的时间戳

### 3. PKT_DTS (Packet Decode Time Stamp)
- **位置**: `AVFrame.pkt_dts`
- **含义**: 这个帧对应的原始数据包的DTS
- **特点**: 从输入包传递到帧的DTS信息

## 📊 数据流程图

```
输入阶段:
AVPacket (input)
├── pts = 1000    (显示时间)
├── dts = 800     (解码时间)
└── data...

解码后:
AVFrame (decoded)
├── pts = 1000    (继承自输入包的pts)
├── pkt_dts = 800 (继承自输入包的dts)
└── data...

编码前设置:
AVFrame (for encoding)
├── pts = 2000        (新的显示时间)
├── pkt_dts = 2000    (告诉编码器的解码时间)
└── data...

编码后:
AVPacket (output)
├── pts = 2000    (由编码器根据AVFrame.pts设置)
├── dts = 2000    (由编码器根据AVFrame.pkt_dts设置)
└── data...
```

## 🔧 代码中的具体作用

### 设置 AVFrame.pkt_dts
```cpp
output_frame->pts = output_pts;      // 告诉编码器何时显示
output_frame->pkt_dts = output_pts;  // 告诉编码器何时解码
```

**为什么设置相同的值？**
- 对于P帧和I帧：DTS = PTS（解码时间 = 显示时间）
- 对于B帧：DTS < PTS（需要提前解码，稍后显示）
- 我们的场景主要是I帧和P帧，所以设置相同值

### 检查 AVPacket.dts
```cpp
if (out_packet->dts > out_packet->pts) {
    out_packet->dts = out_packet->pts;  // 确保 DTS <= PTS
}
```

**为什么需要这个检查？**
- 编码器可能产生错误的DTS
- 确保解码时间不晚于显示时间
- 避免播放器错误

## 🎬 实际例子

### 场景：跳过第2帧和第4帧

**输入视频帧序列：**
```
帧1: PTS=0,    DTS=0    (保留)
帧2: PTS=1000, DTS=1000 (跳过)
帧3: PTS=2000, DTS=2000 (保留)
帧4: PTS=3000, DTS=3000 (跳过)  
帧5: PTS=4000, DTS=4000 (保留)
```

**错误的处理方式：**
```cpp
// 错误：直接复制原始时间戳
output_frame->pts = input_frame->pts;        // 0, 2000, 4000
output_frame->pkt_dts = input_frame->pkt_dts; // 0, 2000, 4000
// 结果：时间戳跳跃，播放卡顿
```

**正确的处理方式：**
```cpp
// 正确：重新计算连续的时间戳
output_frame->pts = output_pts;      // 0, 1000, 2000
output_frame->pkt_dts = output_pts;  // 0, 1000, 2000
output_pts += frame_duration;        // 递增1000
// 结果：时间戳连续，播放流畅
```

## 🔍 为什么不能省略 pkt_dts？

### 如果不设置 pkt_dts：
```cpp
output_frame->pts = output_pts;
// output_frame->pkt_dts = ???  // 未设置，可能是随机值
```

**可能的问题：**
1. 编码器使用错误的DTS
2. 输出包的DTS不正确
3. 播放器解码顺序混乱
4. 播放卡顿或错误

### 设置 pkt_dts 的好处：
```cpp
output_frame->pts = output_pts;
output_frame->pkt_dts = output_pts;  // 明确告诉编码器
```

**确保：**
1. 编码器知道正确的解码时间
2. 输出包的DTS正确
3. 播放器能正确解码
4. 播放流畅无卡顿

## 🎯 最佳实践

### 1. 对于简单视频（无B帧）
```cpp
// I帧和P帧：DTS = PTS
output_frame->pts = output_pts;
output_frame->pkt_dts = output_pts;
```

### 2. 对于复杂视频（有B帧）
```cpp
// 需要更复杂的DTS计算
int64_t dts = calculate_dts_for_b_frames(output_pts, frame_type);
output_frame->pts = output_pts;
output_frame->pkt_dts = dts;
```

### 3. 安全检查
```cpp
// 编码后检查输出包
if (out_packet->dts > out_packet->pts) {
    out_packet->dts = out_packet->pts;  // 修正错误
}
```

## 🚨 常见错误

### 错误1：混淆概念
```cpp
// 错误：把AVFrame的dts当作pkt_dts
output_frame->dts = output_pts;  // AVFrame没有dts字段！
```

### 错误2：不设置pkt_dts
```cpp
// 错误：只设置pts
output_frame->pts = output_pts;
// 缺少：output_frame->pkt_dts = output_pts;
```

### 错误3：时间戳不一致
```cpp
// 错误：pts和pkt_dts不匹配
output_frame->pts = output_pts;
output_frame->pkt_dts = some_other_value;  // 可能导致问题
```

## 📝 总结

| 字段 | 位置 | 作用 | 设置时机 |
|------|------|------|----------|
| `AVFrame.pts` | 帧 | 显示时间 | 编码前 |
| `AVFrame.pkt_dts` | 帧 | 解码时间提示 | 编码前 |
| `AVPacket.pts` | 包 | 显示时间 | 编码器输出 |
| `AVPacket.dts` | 包 | 解码时间 | 编码器输出 |

**关键点：**
- `pkt_dts` 是给编码器的"提示"
- `dts` 是编码器的"输出"
- 两者都很重要，缺一不可
- 正确设置才能避免播放卡顿
