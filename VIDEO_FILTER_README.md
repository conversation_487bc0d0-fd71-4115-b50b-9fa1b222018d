# 视频角点过滤器

这个程序可以分析视频中每一帧的角点数量，并过滤掉角点数量超过阈值的帧，生成一个新的视频文件。

## 功能特点

- 使用Harris角点检测算法分析每一帧
- 自动过滤角点数量大于10的帧
- 保持原视频的分辨率和帧率
- 支持多种视频格式
- 提供处理进度和统计信息

## 编译方法

### 方法1: 使用CMake构建系统

```bash
# 在项目根目录下
mkdir build
cd build
cmake ..
make video_filter_opencv
```

### 方法2: 直接编译 (需要安装OpenCV)

```bash
g++ -std=c++17 src/video_filter_opencv.cpp -o video_filter_opencv `pkg-config --cflags --libs opencv4`
```

## 使用方法

```bash
./video_filter_opencv <输入视频文件> <输出视频文件>
```

### 示例

```bash
# 处理MP4文件
./video_filter_opencv input.mp4 output.mp4

# 处理AVI文件
./video_filter_opencv input.avi output.avi

# 处理MOV文件
./video_filter_opencv input.mov output.mov
```

## 程序输出

程序运行时会显示以下信息：

1. **输入视频信息**：
   - 分辨率
   - 帧率
   - 总帧数

2. **处理进度**：
   - 每30帧显示一次当前进度
   - 显示当前帧的角点数量

3. **最终统计**：
   - 总帧数
   - 过滤帧数
   - 保留帧数
   - 过滤比例

### 示例输出

```
视频角点过滤器 v1.0
功能: 过滤掉角点数量大于10的视频帧

输入文件: input.mp4
输出文件: output.mp4

输入视频信息:
  分辨率: 1920x1080
  帧率: 30 FPS
  总帧数: 900

开始处理视频...
已处理帧数: 30, 当前帧角点数: 5
已处理帧数: 60, 当前帧角点数: 12
已处理帧数: 90, 当前帧角点数: 8
...

处理完成!
总帧数: 900
过滤帧数: 150
保留帧数: 750
过滤比例: 16.7%

视频处理成功完成!
输出文件已保存为: output.mp4
```

## 算法说明

### 角点检测算法

程序使用Harris角点检测算法来识别图像中的角点：

1. **灰度转换**：将彩色帧转换为灰度图像
2. **Harris角点检测**：使用OpenCV的`cornerHarris`函数
3. **阈值处理**：使用阈值0.010来确定角点
4. **角点计数**：统计检测到的角点数量

### 过滤策略

- **阈值**：角点数量 > 10
- **动作**：删除该帧（不写入输出视频）
- **保留**：角点数量 ≤ 10的帧写入输出视频

## 性能优化建议

1. **减少卡顿的关键因素**：
   - 使用合适的视频编码器（H.264优先，MJPG备选）
   - 保持原始帧率
   - 避免不必要的格式转换

2. **编码器选择**：
   - 优先使用H.264编码器（更好的压缩率）
   - 如果H.264不可用，自动降级到MJPG
   - 保持与输入视频相同的分辨率和帧率

3. **内存管理**：
   - 逐帧处理，避免加载整个视频到内存
   - 及时释放处理完的帧

## 故障排除

### 常见问题

1. **无法打开输入文件**
   - 检查文件路径是否正确
   - 确认文件格式被OpenCV支持
   - 检查文件是否损坏

2. **无法创建输出文件**
   - 检查输出目录是否存在且有写权限
   - 确认磁盘空间充足
   - 尝试不同的输出文件名

3. **编码器问题**
   - 程序会自动尝试H.264和MJPG编码器
   - 如果都失败，检查OpenCV是否正确安装

### 依赖要求

- **OpenCV 3.2或更高版本**
- **C++17编译器**
- **CMake 3.15或更高版本**（如果使用CMake构建）

## 自定义修改

如果需要修改角点阈值，可以编辑源代码中的以下行：

```cpp
// 在processVideo()函数中
if (corner_count <= 10) {  // 修改这里的数值10
    output_writer.write(frame);
    processed_count++;
} else {
    filtered_count++;
}
```

如果需要修改Harris角点检测参数，可以编辑`iCheckMosaicImage`函数：

```cpp
cornerHarris(srcFrameGray, srcFrameGray, 2, 3, 0.04);  // 修改这些参数
threshold(srcFrameGray, srcFrameGray, 0.010, 255, cv::THRESH_BINARY);  // 修改阈值
```

## 许可证

请参考项目根目录的LICENSE文件。
