#include <opencv2/opencv.hpp>
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libswscale/swscale.h>
#include <libavutil/imgutils.h>
}
int  iCheckMosaicImage(const Mat& srcFrame)
{
    Mat srcFrameGray;

    int l_iTotalCorner = 0;

    cvtColor(srcFrame, srcFrameGray, COLOR_BGR2GRAY);

    cornerHarris(srcFrameGray, srcFrameGray, 2, 3, 0.04);
    threshold(srcFrameGray, srcFrameGray, 0.010, 255, cv::THRESH_BINARY);

    //imshow("srcFrameGray", srcFrameGray);

    int l_iRow = srcFrameGray.rows;
    int l_iCol = srcFrameGray.cols;


    for (int i = 0; i < l_iRow; i++)
    {
        for (int j = 0; j < l_iCol; j++)
        {
            if (srcFrameGray.at<float>(i, j) == 255)//值255为角点
            {
                l_iTotalCorner++;
            }
        }
    }

    return l_iTotalCorner;

}
static cv::Mat avFrameToCvMat(AVFrame* frame, SwsContext* sws_ctx) {
    cv::Mat bgr(frame->height, frame->width, CV_8UC3);
    // sws_ctx = sws_getCachedContext(sws_ctx, frame->width, frame->height, (AVPixelFormat)frame->format,
    //                 frame->width, frame->height, AV_PIX_FMT_BGR24,
    //                 SWS_BICUBIC, nullptr, nullptr, nullptr);
    int stride = 3 * frame->width;
    sws_scale(sws_ctx, (const uint8_t* const*)frame->data, frame->linesize, 0, frame->height, &bgr.data,
        &stride);

    //sws_freeContext(sws_ctx);
    return bgr;
}