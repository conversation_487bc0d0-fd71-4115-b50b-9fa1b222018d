#include <iostream>
#include <string>
#include <opencv2/opencv.hpp>

using namespace cv;
using namespace std;

// 从CornerTest.cpp复制的角点检测函数
int iCheckMosaicImage(const Mat& srcFrame)
{
    Mat srcFrameGray;
    int l_iTotalCorner = 0;

    cvtColor(srcFrame, srcFrameGray, COLOR_BGR2GRAY);
    cornerHarris(srcFrameGray, srcFrameGray, 2, 3, 0.04);
    threshold(srcFrameGray, srcFrameGray, 0.010, 255, cv::THRESH_BINARY);

    int l_iRow = srcFrameGray.rows;
    int l_iCol = srcFrameGray.cols;

    for (int i = 0; i < l_iRow; i++)
    {
        for (int j = 0; j < l_iCol; j++)
        {
            if (srcFrameGray.at<float>(i, j) == 255) // 值255为角点
            {
                l_iTotalCorner++;
            }
        }
    }

    return l_iTotalCorner;
}

class VideoFilterProcessor 
{
private:
    VideoCapture input_cap;
    VideoWriter output_writer;
    string input_file;
    string output_file;
    
public:
    VideoFilterProcessor(const string& input, const string& output) 
        : input_file(input), output_file(output) {}
    
    bool initialize() {
        // 打开输入视频
        input_cap.open(input_file);
        if (!input_cap.isOpened()) {
            cerr << "错误: 无法打开输入视频文件: " << input_file << endl;
            return false;
        }
        
        // 获取视频属性
        int frame_width = static_cast<int>(input_cap.get(CAP_PROP_FRAME_WIDTH));
        int frame_height = static_cast<int>(input_cap.get(CAP_PROP_FRAME_HEIGHT));
        double fps = input_cap.get(CAP_PROP_FPS);
        int total_frames = static_cast<int>(input_cap.get(CAP_PROP_FRAME_COUNT));
        
        cout << "输入视频信息:" << endl;
        cout << "  分辨率: " << frame_width << "x" << frame_height << endl;
        cout << "  帧率: " << fps << " FPS" << endl;
        cout << "  总帧数: " << total_frames << endl;
        
        // 设置输出视频编码器
        // 使用H.264编码器，如果不可用则使用默认编码器
        int fourcc = VideoWriter::fourcc('H', '2', '6', '4');
        
        // 初始化输出视频写入器
        output_writer.open(output_file, fourcc, fps, Size(frame_width, frame_height), true);
        if (!output_writer.isOpened()) {
            cerr << "错误: 无法创建输出视频文件: " << output_file << endl;
            cerr << "尝试使用其他编码器..." << endl;
            
            // 尝试使用MJPG编码器
            fourcc = VideoWriter::fourcc('M', 'J', 'P', 'G');
            output_writer.open(output_file, fourcc, fps, Size(frame_width, frame_height), true);
            
            if (!output_writer.isOpened()) {
                cerr << "错误: 无法使用任何编码器创建输出视频文件" << endl;
                return false;
            }
        }
        
        return true;
    }
    
    bool processVideo() {
        Mat frame;
        int frame_count = 0;
        int filtered_count = 0;
        int processed_count = 0;
        
        cout << "\n开始处理视频..." << endl;
        
        while (true) {
            // 读取下一帧
            bool ret = input_cap.read(frame);
            if (!ret || frame.empty()) {
                break; // 视频结束
            }
            
            frame_count++;
            
            // 检测角点数量
            int corner_count = iCheckMosaicImage(frame);
            
            // 显示进度
            if (frame_count % 30 == 0) { // 每30帧显示一次进度
                cout << "已处理帧数: " << frame_count << ", 当前帧角点数: " << corner_count << endl;
            }
            
            // 如果角点数量小于等于10，则保留该帧
            if (corner_count <= 10) {
                output_writer.write(frame);
                processed_count++;
            } else {
                filtered_count++;
            }
        }
        
        cout << "\n处理完成!" << endl;
        cout << "总帧数: " << frame_count << endl;
        cout << "过滤帧数: " << filtered_count << endl;
        cout << "保留帧数: " << processed_count << endl;
        cout << "过滤比例: " << (filtered_count * 100.0 / frame_count) << "%" << endl;
        
        return true;
    }
    
    void cleanup() {
        if (input_cap.isOpened()) {
            input_cap.release();
        }
        if (output_writer.isOpened()) {
            output_writer.release();
        }
    }
    
    ~VideoFilterProcessor() {
        cleanup();
    }
};

int main(int argc, char* argv[]) {
    cout << "视频角点过滤器 v1.0" << endl;
    cout << "功能: 过滤掉角点数量大于10的视频帧" << endl << endl;
    
    if (argc != 3) {
        cout << "用法: " << argv[0] << " <输入视频文件> <输出视频文件>" << endl;
        cout << "示例: " << argv[0] << " input.mp4 output.mp4" << endl;
        return -1;
    }
    
    string input_file = argv[1];
    string output_file = argv[2];
    
    // 检查输入文件是否存在
    VideoCapture test_cap(input_file);
    if (!test_cap.isOpened()) {
        cerr << "错误: 输入文件不存在或无法读取: " << input_file << endl;
        return -1;
    }
    test_cap.release();
    
    cout << "输入文件: " << input_file << endl;
    cout << "输出文件: " << output_file << endl << endl;
    
    VideoFilterProcessor processor(input_file, output_file);
    
    if (!processor.initialize()) {
        cerr << "初始化失败" << endl;
        return -1;
    }
    
    if (!processor.processVideo()) {
        cerr << "处理视频失败" << endl;
        return -1;
    }
    
    cout << "\n视频处理成功完成!" << endl;
    cout << "输出文件已保存为: " << output_file << endl;
    
    return 0;
}
