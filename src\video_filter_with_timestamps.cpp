#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <iomanip>
#include <opencv2/opencv.hpp>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libswscale/swscale.h>
#include <libavutil/imgutils.h>
#include <libavutil/opt.h>
}

using namespace cv;
using namespace std;

// 角点检测函数
int iCheckMosaicImage(const Mat& srcFrame)
{
    Mat srcFrameGray;
    int l_iTotalCorner = 0;

    cvtColor(srcFrame, srcFrameGray, COLOR_BGR2GRAY);
    cornerHarris(srcFrameGray, srcFrameGray, 2, 3, 0.04);
    threshold(srcFrameGray, srcFrameGray, 0.010, 255, cv::THRESH_BINARY);

    int l_iRow = srcFrameGray.rows;
    int l_iCol = srcFrameGray.cols;

    for (int i = 0; i < l_iRow; i++)
    {
        for (int j = 0; j < l_iCol; j++)
        {
            if (srcFrameGray.at<float>(i, j) == 255)
            {
                l_iTotalCorner++;
            }
        }
    }

    return l_iTotalCorner;
}

// AVFrame转Mat函数
cv::Mat avFrameToCvMat(AVFrame* frame, SwsContext* sws_ctx) 
{
    cv::Mat bgr(frame->height, frame->width, CV_8UC3);
    int stride = 3 * frame->width;
    sws_scale(sws_ctx, (const uint8_t* const*)frame->data, frame->linesize, 0, frame->height, &bgr.data, &stride);
    return bgr;
}

// Mat转AVFrame函数
AVFrame* cvMatToAVFrame(const cv::Mat& mat, SwsContext* sws_ctx, AVFrame* frame)
{
    if (!frame) {
        frame = av_frame_alloc();
        frame->format = AV_PIX_FMT_YUV420P;
        frame->width = mat.cols;
        frame->height = mat.rows;
        av_frame_get_buffer(frame, 32);
    }

    // 创建临时BGR24格式的AVFrame
    AVFrame* bgr_frame = av_frame_alloc();
    bgr_frame->format = AV_PIX_FMT_BGR24;
    bgr_frame->width = mat.cols;
    bgr_frame->height = mat.rows;
    av_frame_get_buffer(bgr_frame, 32);

    // 复制Mat数据到BGR24 AVFrame
    int stride = 3 * mat.cols;
    memcpy(bgr_frame->data[0], mat.data, mat.rows * stride);
    bgr_frame->linesize[0] = stride;

    // 转换BGR24到YUV420P
    sws_scale(sws_ctx, (const uint8_t* const*)bgr_frame->data, bgr_frame->linesize, 
              0, mat.rows, frame->data, frame->linesize);

    av_frame_free(&bgr_frame);
    return frame;
}

class TimestampAwareVideoProcessor 
{
private:
    AVFormatContext* input_format_ctx;
    AVFormatContext* output_format_ctx;
    AVCodecContext* decoder_ctx;
    AVCodecContext* encoder_ctx;
    SwsContext* sws_ctx_decode;
    SwsContext* sws_ctx_encode;
    int video_stream_index;
    AVStream* input_stream;
    AVStream* output_stream;
    
    // 时间戳管理
    int64_t output_pts;
    int64_t last_input_pts;
    AVRational input_time_base;
    AVRational output_time_base;
    
    // 统计信息
    int processed_frames;
    int filtered_frames;
    int kept_frames;
    
public:
    TimestampAwareVideoProcessor() : 
        input_format_ctx(nullptr), output_format_ctx(nullptr),
        decoder_ctx(nullptr), encoder_ctx(nullptr),
        sws_ctx_decode(nullptr), sws_ctx_encode(nullptr),
        video_stream_index(-1), input_stream(nullptr), output_stream(nullptr),
        output_pts(0), last_input_pts(0),
        processed_frames(0), filtered_frames(0), kept_frames(0) {}
    
    ~TimestampAwareVideoProcessor() {
        cleanup();
    }
    
    bool initInput(const string& input_file) {
        if (avformat_open_input(&input_format_ctx, input_file.c_str(), nullptr, nullptr) < 0) {
            cerr << "无法打开输入文件: " << input_file << endl;
            return false;
        }
        
        if (avformat_find_stream_info(input_format_ctx, nullptr) < 0) {
            cerr << "无法获取流信息" << endl;
            return false;
        }
        
        video_stream_index = av_find_best_stream(input_format_ctx, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
        if (video_stream_index < 0) {
            cerr << "无法找到视频流" << endl;
            return false;
        }
        
        input_stream = input_format_ctx->streams[video_stream_index];
        input_time_base = input_stream->time_base;
        
        const AVCodec* decoder = avcodec_find_decoder(input_stream->codecpar->codec_id);
        if (!decoder) {
            cerr << "无法找到解码器" << endl;
            return false;
        }
        
        decoder_ctx = avcodec_alloc_context3(decoder);
        if (!decoder_ctx) {
            cerr << "无法分配解码器上下文" << endl;
            return false;
        }
        
        if (avcodec_parameters_to_context(decoder_ctx, input_stream->codecpar) < 0) {
            cerr << "无法复制解码器参数" << endl;
            return false;
        }
        
        if (avcodec_open2(decoder_ctx, decoder, nullptr) < 0) {
            cerr << "无法打开解码器" << endl;
            return false;
        }
        
        sws_ctx_decode = sws_getContext(decoder_ctx->width, decoder_ctx->height, decoder_ctx->pix_fmt,
                                       decoder_ctx->width, decoder_ctx->height, AV_PIX_FMT_BGR24,
                                       SWS_BICUBIC, nullptr, nullptr, nullptr);
        
        return true;
    }
    
    bool initOutput(const string& output_file) {
        avformat_alloc_output_context2(&output_format_ctx, nullptr, nullptr, output_file.c_str());
        if (!output_format_ctx) {
            cerr << "无法创建输出格式上下文" << endl;
            return false;
        }
        
        const AVCodec* encoder = avcodec_find_encoder(AV_CODEC_ID_H264);
        if (!encoder) {
            cerr << "无法找到H264编码器" << endl;
            return false;
        }
        
        output_stream = avformat_new_stream(output_format_ctx, nullptr);
        if (!output_stream) {
            cerr << "无法创建输出流" << endl;
            return false;
        }
        
        encoder_ctx = avcodec_alloc_context3(encoder);
        if (!encoder_ctx) {
            cerr << "无法分配编码器上下文" << endl;
            return false;
        }
        
        // 设置编码器参数
        encoder_ctx->codec_id = AV_CODEC_ID_H264;
        encoder_ctx->bit_rate = decoder_ctx->bit_rate > 0 ? decoder_ctx->bit_rate : 400000;
        encoder_ctx->width = decoder_ctx->width;
        encoder_ctx->height = decoder_ctx->height;
        
        // 关键：设置时间基准
        // 使用更高精度的时间基准以确保平滑播放
        encoder_ctx->time_base = av_make_q(1, 90000); // 90kHz时间基准，常用于视频
        output_time_base = encoder_ctx->time_base;
        
        // 设置帧率 - 重要：保持原始帧率
        if (input_stream->avg_frame_rate.num > 0 && input_stream->avg_frame_rate.den > 0) {
            encoder_ctx->framerate = input_stream->avg_frame_rate;
        } else {
            encoder_ctx->framerate = av_make_q(30, 1); // 默认30fps
        }
        
        encoder_ctx->pix_fmt = AV_PIX_FMT_YUV420P;
        encoder_ctx->gop_size = 12;
        encoder_ctx->max_b_frames = 1;
        
        // 优化设置以减少延迟
        av_opt_set(encoder_ctx->priv_data, "preset", "medium", 0);
        av_opt_set(encoder_ctx->priv_data, "crf", "23", 0);
        av_opt_set(encoder_ctx->priv_data, "tune", "film", 0);
        
        if (output_format_ctx->oformat->flags & AVFMT_GLOBALHEADER) {
            encoder_ctx->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;
        }
        
        if (avcodec_open2(encoder_ctx, encoder, nullptr) < 0) {
            cerr << "无法打开编码器" << endl;
            return false;
        }
        
        if (avcodec_parameters_from_context(output_stream->codecpar, encoder_ctx) < 0) {
            cerr << "无法复制编码器参数" << endl;
            return false;
        }
        
        output_stream->time_base = encoder_ctx->time_base;
        
        sws_ctx_encode = sws_getContext(decoder_ctx->width, decoder_ctx->height, AV_PIX_FMT_BGR24,
                                       encoder_ctx->width, encoder_ctx->height, encoder_ctx->pix_fmt,
                                       SWS_BICUBIC, nullptr, nullptr, nullptr);
        
        if (!(output_format_ctx->oformat->flags & AVFMT_NOFILE)) {
            if (avio_open(&output_format_ctx->pb, output_file.c_str(), AVIO_FLAG_WRITE) < 0) {
                cerr << "无法打开输出文件: " << output_file << endl;
                return false;
            }
        }
        
        if (avformat_write_header(output_format_ctx, nullptr) < 0) {
            cerr << "无法写入文件头" << endl;
            return false;
        }
        
        return true;
    }
    
    bool processVideo() {
        AVPacket* packet = av_packet_alloc();
        AVFrame* frame = av_frame_alloc();
        AVFrame* output_frame = av_frame_alloc();
        
        cout << "开始处理视频（正确处理时间戳）..." << endl;
        cout << "输入时间基准: " << input_time_base.num << "/" << input_time_base.den << endl;
        cout << "输出时间基准: " << output_time_base.num << "/" << output_time_base.den << endl;
        cout << "角点阈值: 10" << endl << endl;
        
        // 计算时间基准转换比例
        double time_base_ratio = av_q2d(input_time_base) / av_q2d(output_time_base);
        
        while (av_read_frame(input_format_ctx, packet) >= 0) {
            if (packet->stream_index == video_stream_index) {
                if (avcodec_send_packet(decoder_ctx, packet) >= 0) {
                    while (avcodec_receive_frame(decoder_ctx, frame) >= 0) {
                        processed_frames++;
                        
                        // 转换为OpenCV Mat
                        cv::Mat mat = avFrameToCvMat(frame, sws_ctx_decode);
                        
                        // 检测角点数量
                        int corner_count = iCheckMosaicImage(mat);
                        
                        if (processed_frames % 30 == 0) {
                            cout << "帧 " << processed_frames << ": 角点=" << corner_count 
                                 << ", 输入PTS=" << frame->pts << ", 输出PTS=" << output_pts << endl;
                        }
                        
                        // 如果角点数量小于等于10，则保留该帧
                        if (corner_count <= 10) {
                            // 转换Mat回AVFrame
                            output_frame = cvMatToAVFrame(mat, sws_ctx_encode, output_frame);
                            
                            // 关键：正确设置时间戳
                            // 输出PTS应该连续递增，不能有跳跃
                            output_frame->pts = output_pts;
                            output_frame->pkt_dts = output_pts;
                            
                            // 编码帧
                            if (avcodec_send_frame(encoder_ctx, output_frame) >= 0) {
                                AVPacket* out_packet = av_packet_alloc();
                                while (avcodec_receive_packet(encoder_ctx, out_packet) >= 0) {
                                    // 重新计算包的时间戳
                                    av_packet_rescale_ts(out_packet, encoder_ctx->time_base, output_stream->time_base);
                                    out_packet->stream_index = output_stream->index;
                                    
                                    // 确保DTS <= PTS
                                    if (out_packet->dts > out_packet->pts) {
                                        out_packet->dts = out_packet->pts;
                                    }
                                    
                                    av_interleaved_write_frame(output_format_ctx, out_packet);
                                }
                                av_packet_free(&out_packet);
                            }
                            
                            kept_frames++;
                            
                            // 递增输出PTS - 关键：保持恒定的帧间隔
                            // 计算每帧的时间间隔（基于目标帧率）
                            int64_t frame_duration = av_rescale_q(1, av_inv_q(encoder_ctx->framerate), output_time_base);
                            output_pts += frame_duration;
                            
                        } else {
                            filtered_frames++;
                            // 注意：跳过的帧不增加output_pts，这样输出视频会更紧凑
                        }
                        
                        last_input_pts = frame->pts;
                    }
                }
            }
            av_packet_unref(packet);
        }
        
        // 刷新编码器
        avcodec_send_frame(encoder_ctx, nullptr);
        AVPacket* out_packet = av_packet_alloc();
        while (avcodec_receive_packet(encoder_ctx, out_packet) >= 0) {
            av_packet_rescale_ts(out_packet, encoder_ctx->time_base, output_stream->time_base);
            out_packet->stream_index = output_stream->index;
            if (out_packet->dts > out_packet->pts) {
                out_packet->dts = out_packet->pts;
            }
            av_interleaved_write_frame(output_format_ctx, out_packet);
        }
        av_packet_free(&out_packet);
        
        av_write_trailer(output_format_ctx);
        
        cout << "\n=== 处理完成 ===" << endl;
        cout << "总帧数: " << processed_frames << endl;
        cout << "保留帧数: " << kept_frames << endl;
        cout << "过滤帧数: " << filtered_frames << endl;
        cout << "过滤比例: " << (double)filtered_frames / processed_frames * 100.0 << "%" << endl;
        cout << "最终输出PTS: " << output_pts << endl;
        
        av_packet_free(&packet);
        av_frame_free(&frame);
        av_frame_free(&output_frame);
        
        return true;
    }
    
    void cleanup() {
        if (sws_ctx_decode) {
            sws_freeContext(sws_ctx_decode);
            sws_ctx_decode = nullptr;
        }
        if (sws_ctx_encode) {
            sws_freeContext(sws_ctx_encode);
            sws_ctx_encode = nullptr;
        }
        if (decoder_ctx) {
            avcodec_free_context(&decoder_ctx);
        }
        if (encoder_ctx) {
            avcodec_free_context(&encoder_ctx);
        }
        if (input_format_ctx) {
            avformat_close_input(&input_format_ctx);
        }
        if (output_format_ctx) {
            if (!(output_format_ctx->oformat->flags & AVFMT_NOFILE)) {
                avio_closep(&output_format_ctx->pb);
            }
            avformat_free_context(output_format_ctx);
        }
    }
};

int main(int argc, char* argv[]) {
    if (argc != 3) {
        cout << "用法: " << argv[0] << " <输入视频文件> <输出视频文件>" << endl;
        return -1;
    }
    
    string input_file = argv[1];
    string output_file = argv[2];
    
    cout << "========================================" << endl;
    cout << "  时间戳感知的视频角点过滤器 v3.0" << endl;
    cout << "    正确处理PTS/DTS，避免播放问题" << endl;
    cout << "========================================" << endl << endl;
    
    TimestampAwareVideoProcessor processor;
    
    if (!processor.initInput(input_file)) {
        cerr << "初始化输入失败" << endl;
        return -1;
    }
    
    if (!processor.initOutput(output_file)) {
        cerr << "初始化输出失败" << endl;
        return -1;
    }
    
    if (!processor.processVideo()) {
        cerr << "处理视频失败" << endl;
        return -1;
    }
    
    cout << "\n✅ 视频处理成功完成!" << endl;
    cout << "💡 输出视频的时间戳已正确处理，应该播放流畅" << endl;
    
    return 0;
}
