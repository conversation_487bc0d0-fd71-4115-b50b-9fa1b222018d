@echo off
echo ========================================
echo     编译视频角点过滤器
echo ========================================
echo.

REM 检查是否存在OpenCV
echo 检查OpenCV安装...
pkg-config --exists opencv4
if %errorlevel% neq 0 (
    echo 警告: 未找到opencv4，尝试opencv...
    pkg-config --exists opencv
    if %errorlevel% neq 0 (
        echo 错误: 未找到OpenCV库
        echo 请确保已安装OpenCV并配置了pkg-config
        pause
        exit /b 1
    )
    set OPENCV_PKG=opencv
) else (
    set OPENCV_PKG=opencv4
)

echo 找到OpenCV: %OPENCV_PKG%
echo.

REM 创建build目录
if not exist build mkdir build
cd build

echo 开始编译...
echo.

REM 方法1: 使用CMake (推荐)
echo === 方法1: 使用CMake ===
cmake .. -DCMAKE_BUILD_TYPE=Release
if %errorlevel% equ 0 (
    echo CMake配置成功，开始构建...
    cmake --build . --target smooth_video_filter --config Release
    if %errorlevel% equ 0 (
        echo.
        echo ✅ 编译成功！
        echo 可执行文件位置: build\src\Release\smooth_video_filter.exe
        echo 或者: build\src\smooth_video_filter.exe
        goto :success
    ) else (
        echo CMake构建失败，尝试方法2...
    )
) else (
    echo CMake配置失败，尝试方法2...
)

echo.
echo === 方法2: 直接编译 ===
cd ..

REM 获取OpenCV编译标志
for /f "delims=" %%i in ('pkg-config --cflags %OPENCV_PKG%') do set OPENCV_CFLAGS=%%i
for /f "delims=" %%i in ('pkg-config --libs %OPENCV_PKG%') do set OPENCV_LIBS=%%i

echo 编译标志: %OPENCV_CFLAGS%
echo 链接库: %OPENCV_LIBS%
echo.

REM 编译程序
echo 编译 smooth_video_filter (OpenCV版本)...
g++ -std=c++17 -O2 src/smooth_video_filter.cpp -o smooth_video_filter %OPENCV_CFLAGS% %OPENCV_LIBS%

if %errorlevel% equ 0 (
    echo ✅ OpenCV版本编译成功: smooth_video_filter.exe
) else (
    echo ❌ OpenCV版本编译失败
)

echo.
echo 检查FFmpeg库...
pkg-config --exists libavcodec libavformat libswscale libavutil
if %errorlevel% equ 0 (
    echo 找到FFmpeg，编译时间戳感知版本...
    for /f "delims=" %%i in ('pkg-config --cflags libavcodec libavformat libswscale libavutil') do set FFMPEG_CFLAGS=%%i
    for /f "delims=" %%i in ('pkg-config --libs libavcodec libavformat libswscale libavutil') do set FFMPEG_LIBS=%%i

    g++ -std=c++17 -O2 src/video_filter_with_timestamps.cpp -o video_filter_with_timestamps %OPENCV_CFLAGS% %FFMPEG_CFLAGS% %OPENCV_LIBS% %FFMPEG_LIBS%

    if %errorlevel% equ 0 (
        echo ✅ 时间戳感知版本编译成功: video_filter_with_timestamps.exe
        echo.
        echo 🎯 推荐使用时间戳感知版本以获得最佳效果！
        goto :success
    ) else (
        echo ❌ 时间戳感知版本编译失败，使用OpenCV版本
        if exist smooth_video_filter.exe (
            goto :success
        )
    )
) else (
    echo FFmpeg未找到，仅使用OpenCV版本
    if exist smooth_video_filter.exe (
        goto :success
    )
)

if not exist smooth_video_filter.exe (
    echo.
    echo ❌ 编译失败
    echo.
    echo 可能的解决方案:
    echo 1. 确保已安装OpenCV开发库
    echo 2. 确保已安装MinGW或Visual Studio
    echo 3. 确保pkg-config正确配置
    echo 4. 尝试手动指定OpenCV路径
    goto :error
)

:success
echo.
echo ========================================
echo           编译完成！
echo ========================================
echo.
echo 可用程序:
if exist video_filter_with_timestamps.exe (
    echo   🎯 video_filter_with_timestamps.exe ^(推荐^)
    echo      - 正确处理时间戳，避免播放卡顿
    echo      - 用法: video_filter_with_timestamps.exe input.mp4 output.mp4
    echo.
)
if exist smooth_video_filter.exe (
    echo   📹 smooth_video_filter.exe ^(OpenCV版本^)
    echo      - 简单易用，但可能有轻微时间戳问题
    echo      - 用法: smooth_video_filter.exe input.mp4 output.mp4
    echo.
)
echo 推荐优先级:
echo   1. video_filter_with_timestamps.exe ^(最佳效果^)
echo   2. smooth_video_filter.exe ^(备选方案^)
echo.
pause
exit /b 0

:error
echo.
echo ========================================
echo           编译失败
echo ========================================
echo.
echo 请检查以下项目:
echo 1. OpenCV是否正确安装
echo 2. 编译器是否可用 (g++ 或 Visual Studio)
echo 3. pkg-config是否正确配置
echo.
echo 如需帮助，请查看 VIDEO_FILTER_README.md
echo.
pause
exit /b 1
